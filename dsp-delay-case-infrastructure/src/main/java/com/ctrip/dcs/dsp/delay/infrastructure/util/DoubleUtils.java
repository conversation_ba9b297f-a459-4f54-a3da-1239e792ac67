package com.ctrip.dcs.dsp.delay.infrastructure.util;

import java.text.DecimalFormat;

public class DoubleUtils {

    /**
     * double 转 String，不格式化
     */
    public static String toString(double value) {
        return String.valueOf(value);
    }

    /**
     * double 转 String，保留指定小数位数（四舍五入）
     */
    public static String toString(double value, int scale) {
        StringBuilder pattern = new StringBuilder("#.");
        for (int i = 0; i < scale; i++) {
            pattern.append("0");
        }
        DecimalFormat df = new DecimalFormat(pattern.toString());
        return df.format(value);
    }

    public static void main(String[] args) {
        double d = 123.456789;

        System.out.println(toString(d));        // 123.456789
        System.out.println(toString(d, 2));     // 123.46
        System.out.println(toString(d, 4));     // 123.4568
    }
}
